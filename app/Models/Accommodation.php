<?php

namespace App\Models;

use App\Traits\UrlHelpers;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Validation\ValidationException;

class Accommodation extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia, SoftDeletes, UrlHelpers;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'team_id',
        'accommodation_group_id',
        'name',
        'description',
        'address',
        'city',
        'province',
        'post_code',
        'country',
        'min_occupancy',
        'max_occupancy',
        'minimum_booking_notice',
        'minimum_stay',
        'published',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'minimum_stay' => 'integer',
    ];

    /**
     * The model's default attribute values.
     *
     * @var array<string, mixed>
     */
    protected $attributes = [
        'minimum_stay' => 1,
    ];

	protected $appends = ['default_price', 'gallery_images'];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Validate accommodation_group_id is required before saving
        static::saving(function ($accommodation) {
            if (empty($accommodation->accommodation_group_id)) {
                throw ValidationException::withMessages([
                    'accommodation_group_id' => ['An accommodation group is required. Please select or create an accommodation group first.']
                ]);
            }
        });
    }

    public function getDefaultPriceAttribute()
    {
        // First check for individual accommodation default price
        $defaultPrice = $this->prices()
            ->where('type', 'default')
            ->orderByDesc('priority')
            ->first();

        if ($defaultPrice) {
            return number_format($defaultPrice->price, 2, '.', '');
        }

        // If no individual pricing and accommodation belongs to a group, check group pricing
        if ($this->accommodation_group_id) {
            // Load the group relationship if not already loaded
            if (!$this->relationLoaded('group')) {
                $this->load('group.prices');
            }

            // Check for group default price
            if ($this->group && $this->group->prices) {
                $groupDefaultPrice = $this->group->prices()
                    ->where('type', 'default')
                    ->orderByDesc('priority')
                    ->first();

                if ($groupDefaultPrice) {
                    return number_format($groupDefaultPrice->price, 2, '.', '');
                }
            }
        }

        // If no pricing found at all, return 0
        return '0.00';
    }

    public function getGalleryImagesAttribute()
    {
        $media = $this->getMedia('gallery');
        
        if ($media->isNotEmpty()) {
            return $media->map(function ($mediaItem) {
                return $this->formatMediaArray($mediaItem);
            })->toArray();
        }
        
        // Return placeholder if no media
        $placeholderUrl = $this->getFirstMediaUrl('gallery');
        return [
            [
                'id' => null,
                'name' => 'placeholder',
                'file_name' => 'placeholder.jpg',
                'mime_type' => 'image/jpeg',
                'original_url' => $this->ensureFullUrl($placeholderUrl),
                'thumbnail' => $this->ensureFullUrl($placeholderUrl),
                'medium' => $this->ensureFullUrl($placeholderUrl),
                'large' => $this->ensureFullUrl($placeholderUrl),
                'full_url' => $this->ensureFullUrl($placeholderUrl),
            ]
        ];
    }

    private function formatMediaArray(Media $media): array
    {
        return [
            'id' => $media->id,
            'name' => $media->name,
            'file_name' => $media->file_name,
            'mime_type' => $media->mime_type,
            'original_url' => $this->ensureFullUrl($media->getUrl()),
            'thumbnail' => $this->ensureFullUrl($media->getUrl('thumb')),
            'medium' => $this->ensureFullUrl($media->getUrl('medium')),
            'large' => $this->ensureFullUrl($media->getUrl('large')),
            'full_url' => $this->ensureFullUrl($media->getUrl()),
        ];
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Get the group that the accommodation belongs to.
     */
    public function group(): BelongsTo
    {
        return $this->belongsTo(AccommodationGroup::class, 'accommodation_group_id');
    }

    public function prices(): HasMany
    {
        return $this->hasMany(AccommodationPrice::class);
    }

    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    public function unavailablePeriods(): HasMany
    {
        return $this->hasMany(AccommodationUnavailablePeriod::class);
    }

    public function getPriceForDate($date, $occupancy = null): float
    {
        // Use minimum occupancy if no occupancy specified
        $occupancy = $occupancy ?? $this->min_occupancy;

        // First check for individual accommodation date-specific pricing
        $datePrice = $this->prices()
            ->where('type', 'date_range')
            ->where('start_date', '<=', $date)
            ->where('end_date', '>=', $date)
            ->orderByDesc('priority')
            ->first();

        if ($datePrice) {
            return $this->calculatePriceWithOccupancy($datePrice->price, $occupancy, $datePrice->additional_person_price);
        }

        // Then check for individual accommodation day-of-week pricing
        $dayOfWeek = date('w', strtotime($date));
        $dayPrice = $this->prices()
            ->where('type', 'day_of_week')
            ->where('day_of_week', $dayOfWeek)
            ->orderByDesc('priority')
            ->first();

        if ($dayPrice) {
            return $this->calculatePriceWithOccupancy($dayPrice->price, $occupancy, $dayPrice->additional_person_price);
        }

        // Check for individual accommodation default price
        $defaultPrice = $this->prices()
            ->where('type', 'default')
            ->orderByDesc('priority')
            ->first();

        if ($defaultPrice) {
            return $this->calculatePriceWithOccupancy($defaultPrice->price, $occupancy, $defaultPrice->additional_person_price);
        }

        // If no individual pricing is found and accommodation belongs to a group, check group pricing
        if ($this->accommodation_group_id) {
            // Check for group date-specific pricing
            $groupDatePrice = $this->group->prices()
                ->where('type', 'date_range')
                ->where('start_date', '<=', $date)
                ->where('end_date', '>=', $date)
                ->orderByDesc('priority')
                ->first();

            if ($groupDatePrice) {
                return $this->calculatePriceWithOccupancy($groupDatePrice->price, $occupancy, $groupDatePrice->additional_person_price);
            }

            // Check for group day-of-week pricing
            $groupDayPrice = $this->group->prices()
                ->where('type', 'day_of_week')
                ->where('day_of_week', $dayOfWeek)
                ->orderByDesc('priority')
                ->first();

            if ($groupDayPrice) {
                return $this->calculatePriceWithOccupancy($groupDayPrice->price, $occupancy, $groupDayPrice->additional_person_price);
            }

            // Check for group default price
            $groupDefaultPrice = $this->group->prices()
                ->where('type', 'default')
                ->orderByDesc('priority')
                ->first();

            if ($groupDefaultPrice) {
                return $this->calculatePriceWithOccupancy($groupDefaultPrice->price, $occupancy, $groupDefaultPrice->additional_person_price);
            }
        }

        // If no pricing is found at all, return 0
        return 0;
    }

    /**
     * Calculate the price based on occupancy
     *
     * @param float $basePrice The base price for minimum occupancy
     * @param int $occupancy The number of occupants
     * @param float|null $additionalPersonPrice The additional price per person (if null, will be determined from the price model)
     * @return float The calculated price
     */
    public function calculatePriceWithOccupancy(float $basePrice, int $occupancy, ?float $additionalPersonPrice = null): float
    {
        // If occupancy is less than minimum, use minimum occupancy price
        if ($occupancy < $this->min_occupancy) {
            return $basePrice;
        }

        // If occupancy is greater than maximum, cap at maximum
        $effectiveOccupancy = min($occupancy, $this->max_occupancy);

        // Calculate additional price for extra occupants
        $extraOccupants = $effectiveOccupancy - $this->min_occupancy;

        if ($extraOccupants <= 0) {
            return $basePrice;
        }

        // Use the provided additional person price or default to 0
        $additionalPrice = $extraOccupants * ($additionalPersonPrice ?? 0);

        return $basePrice + $additionalPrice;
    }

    /**
     * Register media collections for the model.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('gallery')
            ->useFallbackUrl('/images/placeholder.jpg')
            ->useFallbackPath(public_path('/images/placeholder.jpg'));
    }

    /**
     * Register media conversions for the model.
     *
     * @param Media|null $media
     */
    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->nonQueued()
            ->width(200)
            ->height(200)
            ->sharpen(10);

        $this->addMediaConversion('medium')
            ->nonQueued()
            ->width(800)
            ->height(600);

        $this->addMediaConversion('large')
            ->nonQueued()
            ->width(1200)
            ->height(900);
    }

    /**
     * Scope a query to only include published accommodations.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePublished(Builder $query): Builder
    {
        return $query->where('published', true);
    }
}
