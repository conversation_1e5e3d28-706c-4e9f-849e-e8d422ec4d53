<?php

namespace App\Http\Requests;

use Orion\Http\Requests\Request;
use Illuminate\Support\Str;

class AccommodationRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        // Skip validation for index, show, and destroy methods
        $route = request()->route();
        $routeName = $route ? $route->getName() : '';

        if (Str::endsWith($routeName, ['index', 'show', 'destroy'])) {
            return [];
        }

        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string'],
            'address' => ['required', 'string', 'max:255'],
            'city' => ['required', 'string', 'max:255'],
            'province' => ['required', 'string', 'max:255'],
            'post_code' => ['required', 'string', 'max:20'],
            'country' => ['required', 'string', 'max:255'],
            'accommodation_group_id' => ['required', 'exists:accommodation_groups,id'],
            'published' => ['boolean'],
            'user_id' => ['sometimes', 'exists:users,id'],
            'minimum_booking_notice' => ['sometimes', 'integer', 'min:0'],
            'minimum_stay' => ['sometimes', 'integer', 'min:1'],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'accommodation_group_id.required' => 'An accommodation group is required. Please select or create an accommodation group first.',
            'accommodation_group_id.exists' => 'The selected accommodation group is invalid.',
        ];
    }
}
