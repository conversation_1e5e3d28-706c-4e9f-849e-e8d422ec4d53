<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\AccommodationGroup;
use Illuminate\Support\Facades\Auth;

class EnsureUserHasAccommodationGroups
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();

        // Skip middleware if user is not authenticated
        if (!$user) {
            return $next($request);
        }

        // Skip if already on accommodation groups routes or API routes
        if ($request->routeIs('accommodation-groups.*') || $request->routeIs('api.*')) {
            return $next($request);
        }

        // Skip for certain routes that don't require accommodation groups
        $skipRoutes = [
            'plans.*',
            'profile.*',
            'teams.*',
            'logout',
            'user-profile-information.update',
            'user-password.update',
            'current-user-photo.update',
            'current-user-photo.destroy',
        ];

        foreach ($skipRoutes as $pattern) {
            if ($request->routeIs($pattern)) {
                return $next($request);
            }
        }

        // Check if user has any accommodation groups
        $teamIds = $user->allTeams()->pluck('id')->toArray();

        $hasGroups = AccommodationGroup::where(function ($query) use ($user, $teamIds) {
            $query->where('user_id', $user->id);

            if (!empty($teamIds)) {
                $query->orWhereIn('team_id', $teamIds);
            }
        })->exists();

        // If user has no accommodation groups, redirect to accommodation groups page
        if (!$hasGroups) {
            return redirect()->route('accommodation-groups.index')
                ->with('info', 'Get started by creating your first accommodation group. You\'ll need at least one accommodation group before you can start adding accommodations.');
        }

        return $next($request);
    }
}
