<?php

namespace App\Http\Controllers;

use App\Models\Accommodation;
use App\Models\AccommodationGroup;
use App\Models\AccommodationPrice;
use App\Models\AccommodationUnavailablePeriod;
use App\Models\Booking;
use App\Models\BookingStatus;
use App\Services\AnalyticsCacheService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;

class AccommodationsController extends Controller
{
    /**
     * Display a listing of the user's accommodations.
     *
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $groupId = $request->query('group');

        // Start building the query
        $query = Accommodation::with([
            'prices' => function($query) {
                $query->where('type', 'default');
            },
            'group.prices' => function($query) {
                $query->where('type', 'default');
            }
        ]);

        // Filter by user and teams if user is authenticated
        if ($user) {
            $teamIds = $user->allTeams()->pluck('id')->toArray();

            $query->where(function ($query) use ($user, $teamIds) {
                $query->where('user_id', $user->id);

                if (!empty($teamIds)) {
                    $query->orWhereIn('team_id', $teamIds);
                }
            });

            // Filter by group if specified
            if ($groupId) {
                $query->where('accommodation_group_id', $groupId);
            }
        } else {
            // If no user is authenticated, only show published accommodations
            $query->where('published', true);
        }

        $accommodations = $query->get();

        // Load the first gallery image for each accommodation
        foreach ($accommodations as $accommodation) {
            $firstImage = $accommodation->getFirstMedia('gallery');
            $accommodation->featured_image = $firstImage ? [
                'thumbnail' => $firstImage->getUrl('thumb'),
                'medium' => $firstImage->getUrl('medium'),
                'large' => $firstImage->getUrl('large'),
                'original' => $firstImage->getUrl(),
            ] : null;

            // Add the default price as a property for the frontend
            $accommodation = $accommodation->toArray();

            // Check for individual pricing first
            if ($accommodation['prices'] && count($accommodation['prices']) > 0) {
                $accommodation['default_price'] = $accommodation['prices'][0]['price'];
                $accommodation['price_source'] = 'individual';
            }
            // If no individual pricing, check for group pricing
            elseif (isset($accommodation['group']) && $accommodation['group'] &&
                    isset($accommodation['group']['prices']) && count($accommodation['group']['prices']) > 0) {
                // Find the default price in group prices
                $groupDefaultPrice = null;
                foreach ($accommodation['group']['prices'] as $price) {
                    if ($price['type'] === 'default') {
                        $groupDefaultPrice = $price;
                        break;
                    }
                }

                if ($groupDefaultPrice) {
                    $accommodation['default_price'] = $groupDefaultPrice['price'];
                    $accommodation['price_source'] = 'group';
                } else {
                    $accommodation['default_price'] = 0;
                    $accommodation['price_source'] = 'none';
                }
            }
            // No pricing found
            else {
                $accommodation['default_price'] = 0;
                $accommodation['price_source'] = 'none';
            }
        }

        // Get all groups for the filter dropdown
        $groups = [];
        if ($user) {
            $groups = AccommodationGroup::where(function ($query) use ($user, $teamIds) {
                $query->where('user_id', $user->id);

                if (!empty($teamIds)) {
                    $query->orWhereIn('team_id', $teamIds);
                }
            })->get();
        }

        return Inertia::render('Accommodations/Index', [
            'accommodations' => $accommodations,
            'groups' => $groups,
            'currentGroup' => $groupId ? AccommodationGroup::find($groupId) : null
        ]);
    }

    /**
     * Show the form for creating a new accommodation.
     *
     * @return \Inertia\Response
     */
    public function create(Request $request)
    {
        $user = Auth::user();
        $teamIds = $user->allTeams()->pluck('id')->toArray();

        // Get all groups for the dropdown
        $groups = AccommodationGroup::where(function ($query) use ($user, $teamIds) {
            $query->where('user_id', $user->id);

            if (!empty($teamIds)) {
                $query->orWhereIn('team_id', $teamIds);
            }
        })->get();

        return Inertia::render('Accommodations/Create', [
            'groups' => $groups,
            'preselectedGroup' => $request->query('group') ? (int) $request->query('group') : null
        ]);
    }

    /**
     * Store a newly created accommodation in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'province' => 'required|string|max:255',
            'post_code' => 'required|string|max:20',
            'country' => 'required|string|max:255',
            'minimum_booking_notice' => 'required|integer|min:0',
            'minimum_stay' => 'required|integer|min:1',
            'min_occupancy' => 'required|integer|min:1',
            'max_occupancy' => 'required|integer|min:1|gte:min_occupancy',
            'accommodation_group_id' => 'required|exists:accommodation_groups,id',
            'published' => 'boolean',
        ]);

        $user = Auth::user();

        // Check if the user has reached their accommodation limit
        $subscription = $user->planSubscription('main');
        if ($subscription) {
            $accommodationsFeature = $subscription->plan->features()
                ->where('name', 'Accommodations')
                ->first();

            if ($accommodationsFeature) {
                $currentCount = $user->accommodations()->count();
                $limit = (int) $accommodationsFeature->value;

                if ($currentCount >= $limit) {
                    return redirect()->back()->withErrors([
                        'limit' => "You have reached the maximum number of accommodations ({$limit}) allowed in your current plan. Please upgrade your plan to add more accommodations."
                    ])->withInput();
                }
            }
        }

        $user = Auth::user();

        // Create the accommodation data
        $accommodationData = [
            'user_id' => $user->id,
            'name' => $validated['name'],
            'description' => $validated['description'],
            'address' => $validated['address'],
            'city' => $validated['city'],
            'province' => $validated['province'],
            'post_code' => $validated['post_code'],
            'country' => $validated['country'],
            'minimum_booking_notice' => $validated['minimum_booking_notice'],
            'minimum_stay' => $validated['minimum_stay'],
            'min_occupancy' => $validated['min_occupancy'],
            'max_occupancy' => $validated['max_occupancy'],
            'accommodation_group_id' => $validated['accommodation_group_id'],
            'published' => $validated['published'] ?? false,
        ];

        // Add team_id if user has a current team
        if ($user->currentTeam) {
            $accommodationData['team_id'] = $user->currentTeam->id;
        }

        // Create the accommodation
        Accommodation::create($accommodationData);

        return Redirect::route('accommodations.index')->with('success', 'Accommodation created successfully.');
    }

    /**
     * Display the specified accommodation.
     *
     * @param  \App\Models\Accommodation  $accommodation
     * @return \Inertia\Response
     */
    public function show(Accommodation $accommodation)
    {
        $user = Auth::user();
        $teamIds = $user->allTeams()->pluck('id')->toArray();

        // Check if the accommodation belongs to the authenticated user or their team
        if ($accommodation->user_id !== $user->id && !in_array($accommodation->team_id, $teamIds)) {
            abort(403, 'Unauthorized action.');
        }

        // Load prices, bookings with status, unavailable periods, group prices, and media
        $accommodation->load([
            'prices',
            'bookings' => function($query) {
                $query->orderBy('start_date')->with('status');
            },
            'unavailablePeriods',
            'group.prices' => function($query) {
                $query->orderBy('priority', 'desc');
            }
        ]);

        // Get media with URLs
        $galleryImages = $accommodation->getMedia('gallery')->map(function($media) {
            return [
                'id' => $media->id,
                'name' => $media->name,
                'file_name' => $media->file_name,
                'mime_type' => $media->mime_type,
                'size' => $media->size,
                'original_url' => $media->getUrl(),
                'thumbnail' => $media->getUrl('thumb'),
                'medium' => $media->getUrl('medium'),
                'large' => $media->getUrl('large'),
            ];
        });

        return Inertia::render('Accommodations/Show', [
            'accommodation' => $accommodation,
            'galleryImages' => $galleryImages
        ]);
    }

    /**
     * Upload images to an accommodation's gallery.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Accommodation  $accommodation
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadImages(Request $request, Accommodation $accommodation)
    {
        $user = Auth::user();
        $teamIds = $user->allTeams()->pluck('id')->toArray();

        // Check if the accommodation belongs to the authenticated user or their team
        if ($accommodation->user_id !== $user->id && !in_array($accommodation->team_id, $teamIds)) {
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'images' => 'required|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif,webp|max:2048',
        ]);

        $uploadedImages = [];

        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $media = $accommodation->addMedia($image)
                    ->toMediaCollection('gallery');

                $uploadedImages[] = [
                    'id' => $media->id,
                    'name' => $media->name,
                    'file_name' => $media->file_name,
                    'mime_type' => $media->mime_type,
                    'size' => $media->size,
                    'original_url' => $media->getUrl(),
                    'thumbnail' => $media->getUrl('thumb'),
                    'medium' => $media->getUrl('medium'),
                    'large' => $media->getUrl('large'),
                ];
            }
        }

        return response()->json([
            'success' => true,
            'images' => $uploadedImages,
            'message' => count($uploadedImages) . ' images uploaded successfully'
        ]);
    }

    /**
     * Delete an image from an accommodation's gallery.
     *
     * @param  \App\Models\Accommodation  $accommodation
     * @param  int  $mediaId
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteImage(Accommodation $accommodation, $mediaId)
    {
        $user = Auth::user();
        $teamIds = $user->allTeams()->pluck('id')->toArray();

        // Check if the accommodation belongs to the authenticated user or their team
        if ($accommodation->user_id !== $user->id && !in_array($accommodation->team_id, $teamIds)) {
            abort(403, 'Unauthorized action.');
        }

        $media = $accommodation->getMedia('gallery')->where('id', $mediaId)->first();

        if (!$media) {
            return response()->json([
                'success' => false,
                'message' => 'Image not found'
            ], 404);
        }

        $media->delete();

        return response()->json([
            'success' => true,
            'message' => 'Image deleted successfully'
        ]);
    }

    /**
     * Delete an accommodation (soft delete).
     *
     * @param  \App\Models\Accommodation  $accommodation
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Accommodation $accommodation)
    {
        $user = Auth::user();
        $teamIds = $user->allTeams()->pluck('id')->toArray();

        // Check if the accommodation belongs to the authenticated user or their team
        if ($accommodation->user_id !== $user->id && !in_array($accommodation->team_id, $teamIds)) {
            abort(403, 'Unauthorized action.');
        }

        // Soft delete the accommodation
        $accommodation->delete();

        return Redirect::route('accommodations.index')
            ->with('success', 'Accommodation deleted successfully.');
    }

    /**
     * Show the form for editing the specified accommodation.
     *
     * @param  \App\Models\Accommodation  $accommodation
     * @return \Inertia\Response
     */
    public function edit(Accommodation $accommodation)
    {
        $user = Auth::user();
        $teamIds = $user->allTeams()->pluck('id')->toArray();

        // Check if the accommodation belongs to the authenticated user or their team
        if ($accommodation->user_id !== $user->id && !in_array($accommodation->team_id, $teamIds)) {
            abort(403, 'Unauthorized action.');
        }

        // Get all groups for the dropdown
        $groups = AccommodationGroup::where(function ($query) use ($user, $teamIds) {
            $query->where('user_id', $user->id);

            if (!empty($teamIds)) {
                $query->orWhereIn('team_id', $teamIds);
            }
        })->get();

        return Inertia::render('Accommodations/Edit', [
            'accommodation' => $accommodation,
            'groups' => $groups,
        ]);
    }

    /**
     * Update the specified accommodation in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Accommodation  $accommodation
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Accommodation $accommodation)
    {
        // Check if the accommodation belongs to the authenticated user
        if ($accommodation->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'province' => 'required|string|max:255',
            'post_code' => 'required|string|max:20',
            'country' => 'required|string|max:255',
            'minimum_booking_notice' => 'required|integer|min:0',
            'minimum_stay' => 'required|integer|min:1',
            'min_occupancy' => 'required|integer|min:1',
            'max_occupancy' => 'required|integer|min:1|gte:min_occupancy',
            'accommodation_group_id' => 'required|exists:accommodation_groups,id',
            'published' => 'boolean',
        ]);

        DB::transaction(function () use ($validated, $accommodation) {
            // Update the accommodation
            $accommodation->update([
                'name' => $validated['name'],
                'description' => $validated['description'],
                'address' => $validated['address'],
                'city' => $validated['city'],
                'province' => $validated['province'],
                'post_code' => $validated['post_code'],
                'country' => $validated['country'],
                'minimum_booking_notice' => $validated['minimum_booking_notice'],
                'minimum_stay' => $validated['minimum_stay'],
                'min_occupancy' => $validated['min_occupancy'],
                'max_occupancy' => $validated['max_occupancy'],
                'accommodation_group_id' => $validated['accommodation_group_id'],
                'published' => $validated['published'] ?? false,
            ]);
        });

        return Redirect::route('accommodations.show', $accommodation->id)
            ->with('success', 'Accommodation updated successfully.');
    }

    /**
     * Get all prices for an accommodation.
     *
     * @param  \App\Models\Accommodation  $accommodation
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPrices(Accommodation $accommodation)
    {
        // Check if the accommodation belongs to the authenticated user
        if ($accommodation->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        $prices = $accommodation->prices()->orderBy('priority')->get();

        return response()->json([
            'success' => true,
            'prices' => $prices
        ]);
    }

    /**
     * Store a new price for an accommodation.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Accommodation  $accommodation
     * @return \Illuminate\Http\JsonResponse
     */
    public function storePrice(Request $request, Accommodation $accommodation)
    {
        // Check if the accommodation belongs to the authenticated user
        if ($accommodation->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        $validator = Validator::make($request->all(), [
            'price' => 'required|numeric|min:0',
            'additional_person_price' => 'nullable|numeric|min:0',
            'type' => 'required|string|in:default,date_range,day_of_week',
            'start_date' => 'required_if:type,date_range|date|nullable',
            'end_date' => 'required_if:type,date_range|date|nullable|after_or_equal:start_date',
            'day_of_week' => 'required_if:type,day_of_week|integer|min:0|max:6|nullable',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Get the highest priority
        $highestPriority = $accommodation->prices()->max('priority') ?? -1;

        // Create the price
        $price = AccommodationPrice::create([
            'accommodation_id' => $accommodation->id,
            'price' => $request->price,
            'additional_person_price' => $request->additional_person_price ?? 0,
            'type' => $request->type,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'day_of_week' => $request->day_of_week,
            'priority' => $highestPriority + 1
        ]);

        return response()->json([
            'success' => true,
            'price' => $price,
            'message' => 'Price created successfully'
        ]);
    }

    /**
     * Update a price for an accommodation.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Accommodation  $accommodation
     * @param  int  $priceId
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatePrice(Request $request, Accommodation $accommodation, $priceId)
    {
        // Check if the accommodation belongs to the authenticated user
        if ($accommodation->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        $price = $accommodation->prices()->findOrFail($priceId);

        $validator = Validator::make($request->all(), [
            'price' => 'required|numeric|min:0',
            'additional_person_price' => 'nullable|numeric|min:0',
            'type' => 'required|string|in:default,date_range,day_of_week',
            'start_date' => 'required_if:type,date_range|date|nullable',
            'end_date' => 'required_if:type,date_range|date|nullable|after_or_equal:start_date',
            'day_of_week' => 'required_if:type,day_of_week|integer|min:0|max:6|nullable',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Update the price
        $price->update([
            'price' => $request->price,
            'additional_person_price' => $request->additional_person_price ?? 0,
            'type' => $request->type,
            'start_date' => $request->type === 'date_range' ? $request->start_date : null,
            'end_date' => $request->type === 'date_range' ? $request->end_date : null,
            'day_of_week' => $request->type === 'day_of_week' ? $request->day_of_week : null,
        ]);

        return response()->json([
            'success' => true,
            'price' => $price,
            'message' => 'Price updated successfully'
        ]);
    }

    /**
     * Delete a price for an accommodation.
     *
     * @param  \App\Models\Accommodation  $accommodation
     * @param  int  $priceId
     * @return \Illuminate\Http\JsonResponse
     */
    public function deletePrice(Accommodation $accommodation, $priceId)
    {
        // Check if the accommodation belongs to the authenticated user
        if ($accommodation->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        $price = $accommodation->prices()->findOrFail($priceId);

        // Don't allow deleting the default price if it's the only one
        if ($price->type === 'default' && $accommodation->prices()->count() === 1) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete the default price. At least one price is required.'
            ], 422);
        }

        $price->delete();

        return response()->json([
            'success' => true,
            'message' => 'Price deleted successfully'
        ]);
    }

    /**
     * Update the priorities of prices for an accommodation.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Accommodation  $accommodation
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatePricePriorities(Request $request, Accommodation $accommodation)
    {
        // Check if the accommodation belongs to the authenticated user
        if ($accommodation->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        $validator = Validator::make($request->all(), [
            'prices' => 'required|array',
            'prices.*.id' => 'required|integer|exists:accommodation_prices,id',
            'prices.*.priority' => 'required|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Update the priorities
        foreach ($request->prices as $priceData) {
            $price = AccommodationPrice::find($priceData['id']);

            // Make sure the price belongs to this accommodation
            if ($price && $price->accommodation_id === $accommodation->id) {
                $price->update(['priority' => $priceData['priority']]);
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Price priorities updated successfully'
        ]);
    }

    /**
     * Get all booking statuses.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBookingStatuses()
    {
        $statuses = BookingStatus::all();

        return response()->json([
            'success' => true,
            'statuses' => $statuses
        ]);
    }

    /**
     * Update the status of a booking.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Accommodation  $accommodation
     * @param  int  $bookingId
     * @param  \App\Services\AnalyticsCacheService  $analyticsCacheService
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    public function updateBookingStatus(Request $request, Accommodation $accommodation, $bookingId, AnalyticsCacheService $analyticsCacheService)
    {
        // Check if the accommodation belongs to the authenticated user
        if ($accommodation->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        $validator = Validator::make($request->all(), [
            'booking_status_id' => 'required|integer|exists:booking_statuses,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Find the booking
        $booking = Booking::where('id', $bookingId)
            ->where('accommodation_id', $accommodation->id)
            ->firstOrFail();

        // Capture the original status before updating
        $oldStatusId = $booking->booking_status_id;

        // Update the booking status
        $booking->update([
            'booking_status_id' => $request->booking_status_id,
        ]);

        // Load the status relationship
        $booking->load('status');

        // Clear analytics cache for the accommodation owner since booking status affects conversion metrics
        try {
            // Load the user relationship if not already loaded
            if (!$accommodation->relationLoaded('user')) {
                $accommodation->load('user');
            }

            $analyticsCacheService->clearUserCache($accommodation->user);

        } catch (\Exception $e) {
            // Log the error but don't fail the booking status update
            Log::error('Failed to clear analytics cache after booking status update', [
                'booking_id' => $booking->id,
                'accommodation_id' => $accommodation->id,
                'user_id' => $accommodation->user_id,
                'old_status_id' => $oldStatusId,
                'new_status_id' => $booking->booking_status_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        // Check if this is an Inertia request
        if ($request->wantsJson() && !$request->header('X-Inertia')) {
            // For API requests, return JSON
            return response()->json([
                'success' => true,
                'booking' => $booking,
                'message' => 'Booking status updated successfully'
            ]);
        }

        // For Inertia requests, redirect back to the booking page
        return Redirect::route('bookings.show', $booking->id)
            ->with('success', 'Booking status updated successfully');
    }

    /**
     * Toggle the published status of an accommodation.
     *
     * @param  \App\Models\Accommodation  $accommodation
     * @return \Illuminate\Http\JsonResponse
     */
    public function togglePublish(Accommodation $accommodation)
    {
        $user = Auth::user();
        $teamIds = $user->allTeams()->pluck('id')->toArray();

        // Check if the accommodation belongs to the authenticated user or their team
        if ($accommodation->user_id !== $user->id && !in_array($accommodation->team_id, $teamIds)) {
            // User is not authorized to toggle publish status
            abort(403, 'Unauthorized action.');
        }

        // Check if the user is authorized to publish/unpublish this accommodation
        $canPublish = $user->can('publish', $accommodation);

        if (!$canPublish) {
            // If trying to publish, check if it has a default price
            if (!$accommodation->published) {
                $hasDefaultPrice = $accommodation->prices()
                    ->where('type', 'default')
                    ->where('price', '>', 0)
                    ->exists();

                // If no individual default price, check if the accommodation belongs to a group with a default price
                if (!$hasDefaultPrice && $accommodation->accommodation_group_id) {
                    $hasGroupDefaultPrice = $accommodation->group->prices()
                        ->where('type', 'default')
                        ->where('price', '>', 0)
                        ->exists();

                    // Use group pricing if available
                    $hasDefaultPrice = $hasGroupDefaultPrice;
                }

                // Check if the accommodation has a default price (individual or group)
                if (!$hasDefaultPrice) {
                    return response()->json([
                        'success' => false,
                        'message' => 'This accommodation needs either an individual default price or must belong to a group with a default price before it can be published.'
                    ], 422);
                }

                // Check if the user has reached their published accommodation limit
                $subscription = $user->planSubscription('main');
                // Check if the user has a subscription

                if ($subscription) {
                    $accommodationsFeature = $subscription->plan->features()
                        ->where('name', 'Accommodations')
                        ->first();

                    // Check if the user has the Accommodations feature

                    if ($accommodationsFeature) {
                        $currentCount = $user->accommodations()->where('published', true)->count();
                        $limit = (int) $accommodationsFeature->value;

                        // Check if the user has reached their published accommodation limit

                        if ($currentCount >= $limit) {
                            return response()->json([
                                'success' => false,
                                'message' => "You have reached the maximum number of published accommodations ({$limit}) allowed in your current plan. Please upgrade your plan to publish more accommodations."
                            ], 422);
                        }
                    }
                }
            }

            // If we get here, there's some other reason the user can't publish

            return response()->json([
                'success' => false,
                'message' => 'You are not authorized to change the published status of this accommodation.'
            ], 403);
        }

        // Toggle the published status
        $accommodation->update([
            'published' => !$accommodation->published
        ]);

        return response()->json([
            'success' => true,
            'published' => $accommodation->published,
            'message' => $accommodation->published ? 'Accommodation published successfully.' : 'Accommodation unpublished successfully.'
        ]);
    }

    /**
     * Get all unavailable periods for an accommodation.
     *
     * @param  \App\Models\Accommodation  $accommodation
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUnavailablePeriods(Accommodation $accommodation)
    {
        $user = Auth::user();
        $teamIds = $user->allTeams()->pluck('id')->toArray();

        // Check if the accommodation belongs to the authenticated user or their team
        if ($accommodation->user_id !== $user->id && !in_array($accommodation->team_id, $teamIds)) {
            abort(403, 'Unauthorized action.');
        }

        $periods = $accommodation->unavailablePeriods()->orderBy('created_at', 'desc')->get();

        return response()->json([
            'success' => true,
            'periods' => $periods
        ]);
    }

    /**
     * Store a new unavailable period for an accommodation.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Accommodation  $accommodation
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeUnavailablePeriod(Request $request, Accommodation $accommodation)
    {
        $user = Auth::user();
        $teamIds = $user->allTeams()->pluck('id')->toArray();

        // Check if the accommodation belongs to the authenticated user or their team
        if ($accommodation->user_id !== $user->id && !in_array($accommodation->team_id, $teamIds)) {
            abort(403, 'Unauthorized action.');
        }

        $validator = Validator::make($request->all(), [
            'type' => 'required|in:date_range,recurring_days',
            'start_date' => 'required_if:type,date_range|date|nullable',
            'end_date' => 'required_if:type,date_range|date|nullable|after_or_equal:start_date',
            'days_of_week' => 'required_if:type,recurring_days|array|min:1|nullable',
            'days_of_week.*' => 'integer|between:0,6',
            'reason' => 'nullable|string|max:255',
            'active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Create the unavailable period
        $period = AccommodationUnavailablePeriod::create([
            'accommodation_id' => $accommodation->id,
            'type' => $request->type,
            'start_date' => $request->type === 'date_range' ? $request->start_date : null,
            'end_date' => $request->type === 'date_range' ? $request->end_date : null,
            'days_of_week' => $request->type === 'recurring_days' ?
                (is_array($request->days_of_week) ?
                    array_map('intval', $request->days_of_week) :
                    $request->days_of_week) :
                null,
            'reason' => $request->reason,
            'active' => $request->active ?? true,
        ]);

        return response()->json([
            'success' => true,
            'period' => $period,
            'message' => 'Unavailable period created successfully'
        ]);
    }

    /**
     * Delete an unavailable period for an accommodation.
     *
     * @param  \App\Models\Accommodation  $accommodation
     * @param  int  $periodId
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteUnavailablePeriod(Accommodation $accommodation, $periodId)
    {
        $user = Auth::user();
        $teamIds = $user->allTeams()->pluck('id')->toArray();

        // Check if the accommodation belongs to the authenticated user or their team
        if ($accommodation->user_id !== $user->id && !in_array($accommodation->team_id, $teamIds)) {
            abort(403, 'Unauthorized action.');
        }

        $period = $accommodation->unavailablePeriods()->findOrFail($periodId);
        $period->delete();

        return response()->json([
            'success' => true,
            'message' => 'Unavailable period deleted successfully'
        ]);
    }
}
