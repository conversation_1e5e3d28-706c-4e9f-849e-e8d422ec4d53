<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AccommodationResource\Pages;
use App\Filament\Resources\AccommodationResource\RelationManagers;
use App\Models\Accommodation;
use App\Models\AccommodationGroup;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use Illuminate\Support\Facades\Auth;

class AccommodationResource extends Resource
{
    protected static ?string $model = Accommodation::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office-2';

    protected static ?string $navigationLabel = 'Accommodations';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('accommodation_group_id')
                    ->label('Group')
                    ->relationship('group', 'name')
                    ->options(function () {
                        $user = Auth::user();
                        $teamIds = $user->allTeams()->pluck('id')->toArray();

                        return AccommodationGroup::where(function ($query) use ($user, $teamIds) {
                            $query->where('user_id', $user->id);

                            if (!empty($teamIds)) {
                                $query->orWhereIn('team_id', $teamIds);
                            }
                        })->pluck('name', 'id');
                    })
                    ->createOptionForm([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->nullable(),
                    ])
                    ->createOptionUsing(function (array $data) {
                        $user = Auth::user();

                        return AccommodationGroup::create([
                            'name' => $data['name'],
                            'description' => $data['description'] ?? null,
                            'user_id' => $user->id,
                            'team_id' => $user->currentTeam?->id,
                        ]);
                    })
                    ->required()
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('description')
                    ->nullable(),
                Forms\Components\TextInput::make('address')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('city')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('state')
                    ->nullable()
                    ->maxLength(255),
                Forms\Components\TextInput::make('post_code')
                    ->nullable()
                    ->maxLength(20),
                Forms\Components\TextInput::make('country')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Toggle::make('published')
                    ->default(true),
                Forms\Components\Section::make('Occupancy & Booking Rules')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('min_occupancy')
                                    ->label('Minimum Occupancy')
                                    ->integer()
                                    ->minValue(1)
                                    ->default(1)
                                    ->required(),
                                Forms\Components\TextInput::make('max_occupancy')
                                    ->label('Maximum Occupancy')
                                    ->integer()
                                    ->minValue(1)
                                    ->default(2)
                                    ->required(),
                            ]),
                        Forms\Components\TextInput::make('minimum_booking_notice')
                            ->label('Minimum Booking Notice (days)')
                            ->helperText('Minimum number of days in advance a booking can be made')
                            ->integer()
                            ->minValue(0)
                            ->default(0)
                            ->required(),
                        Forms\Components\TextInput::make('minimum_stay')
                            ->label('Minimum Stay (nights)')
                            ->integer()
                            ->minValue(1)
                            ->default(1)
                            ->required(),
                    ]),
                SpatieMediaLibraryFileUpload::make('gallery')
                    ->collection('gallery')
                    ->multiple()
                    ->maxFiles(10)
                    ->reorderable()
                    ->columnSpanFull()
                    ->label('Image Gallery')
                    ->helperText('Upload images of your accommodation (max 10 images)'),
                Forms\Components\Section::make('Pricing')
                    ->schema([
                        Forms\Components\Repeater::make('prices')
                            ->relationship('prices')
                            ->schema([
                                Forms\Components\TextInput::make('price')
                                    ->numeric()
                                    ->required()
                                    ->label('Price per night (for minimum occupancy)'),
                                Forms\Components\TextInput::make('additional_person_price')
                                    ->numeric()
                                    ->default(0)
                                    ->required()
                                    ->label('Additional price per person')
                                    ->helperText('Extra charge per person above minimum occupancy'),
                                Forms\Components\Select::make('type')
                                    ->options([
                                        'default' => 'Default Price',
                                        'date_range' => 'Date Range Price',
                                        'day_of_week' => 'Day of Week Price',
                                    ])
                                    ->required()
                                    ->reactive(),
                                Forms\Components\DatePicker::make('start_date')
                                    ->visible(fn (\Filament\Forms\Get $get) => $get('type') === 'date_range')
                                    ->required(fn (\Filament\Forms\Get $get) => $get('type') === 'date_range'),
                                Forms\Components\DatePicker::make('end_date')
                                    ->visible(fn (\Filament\Forms\Get $get) => $get('type') === 'date_range')
                                    ->required(fn (\Filament\Forms\Get $get) => $get('type') === 'date_range'),
                                Forms\Components\Select::make('day_of_week')
                                    ->visible(fn (\Filament\Forms\Get $get) => $get('type') === 'day_of_week')
                                    ->options([
                                        0 => 'Sunday',
                                        1 => 'Monday',
                                        2 => 'Tuesday',
                                        3 => 'Wednesday',
                                        4 => 'Thursday',
                                        5 => 'Friday',
                                        6 => 'Saturday',
                                    ])
                                    ->required(fn (\Filament\Forms\Get $get) => $get('type') === 'day_of_week'),
                                Forms\Components\TextInput::make('priority')
                                    ->integer()
                                    ->default(0),
                            ])
                            ->columnSpanFull(),
                    ]),
                Forms\Components\Section::make('Unavailable Periods')
                    ->schema([
                        Forms\Components\Repeater::make('unavailablePeriods')
                            ->relationship('unavailablePeriods')
                            ->schema([
                                Forms\Components\Select::make('type')
                                    ->options([
                                        'date_range' => 'Date Range',
                                        'recurring_days' => 'Recurring Days',
                                    ])
                                    ->required()
                                    ->reactive(),

                                Forms\Components\DatePicker::make('start_date')
                                    ->visible(fn (\Filament\Forms\Get $get) => $get('type') === 'date_range')
                                    ->required(fn (\Filament\Forms\Get $get) => $get('type') === 'date_range'),

                                Forms\Components\DatePicker::make('end_date')
                                    ->visible(fn (\Filament\Forms\Get $get) => $get('type') === 'date_range')
                                    ->required(fn (\Filament\Forms\Get $get) => $get('type') === 'date_range'),

                                Forms\Components\CheckboxList::make('days_of_week')
                                    ->visible(fn (\Filament\Forms\Get $get) => $get('type') === 'recurring_days')
                                    ->required(fn (\Filament\Forms\Get $get) => $get('type') === 'recurring_days')
                                    ->options([
                                        0 => 'Sunday',
                                        1 => 'Monday',
                                        2 => 'Tuesday',
                                        3 => 'Wednesday',
                                        4 => 'Thursday',
                                        5 => 'Friday',
                                        6 => 'Saturday',
                                    ]),

                                Forms\Components\TextInput::make('reason')
                                    ->maxLength(255),

                                Forms\Components\Toggle::make('active')
                                    ->default(true),
                            ])
                            ->columnSpanFull()
                            ->itemLabel(fn (array $state): ?string =>
                                $state['type'] === 'date_range'
                                    ? "Date Range: {$state['start_date']} to {$state['end_date']}"
                                    : "Recurring Days"
                            ),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('group.name')
                    ->label('Group')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('address')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('city')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('state')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('post_code')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('country')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('default_price')
                    ->money('ZAR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('min_occupancy')
                    ->label('Min Occupancy')
                    ->sortable(),
                Tables\Columns\TextColumn::make('max_occupancy')
                    ->label('Max Occupancy')
                    ->sortable(),
                Tables\Columns\TextColumn::make('minimum_booking_notice')
                    ->label('Min. Booking Notice')
                    ->suffix(' days')
                    ->sortable(),
                Tables\Columns\TextColumn::make('minimum_stay')
                    ->label('Min. Stay')
                    ->suffix(' nights')
                    ->sortable(),
                Tables\Columns\IconColumn::make('published')
                    ->boolean()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->recordUrl(fn(Accommodation $record): string => static::getUrl('edit', ['record' => $record]));
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAccommodations::route('/'),
            'create' => Pages\CreateAccommodation::route('/create'),
            'edit' => Pages\EditAccommodation::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);

        // Filter accommodations based on team membership
        $user = Auth::user();

        if (!$user) {
            // If no user is authenticated, return an empty query
            return $query->whereRaw('1 = 0');
        }

        if (!$user->is_admin) {
            $teamIds = $user->allTeams()->pluck('id')->toArray();

            $query->where(function ($query) use ($user, $teamIds) {
                $query->where('user_id', $user->id)
                    ->orWhereIn('team_id', $teamIds);
            });
        }

        return $query;
    }
}
