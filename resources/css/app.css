@import url('https://fonts.googleapis.com/css2?family=Galindo&display=swap');
@import 'tailwindcss';
@import "preline/variants.css";
@import "../../node_modules/preline/src/plugins/datepicker/styles.css";

@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/typography';

@source "../../node_modules/preline/dist/*.js";
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../vendor/laravel/jetstream/**/*.blade.php';

/* Enable dark mode */
:root {
  color-scheme: light dark;
}

@custom-variant dark (&:where(.dark, .dark *));

@theme {
  --font-sans: Figtree, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-galindo: Galindo, sans-serif;

  /* Light mode colors */
  --color-background: #E5F3FF;
  --color-primary: #003B5C;
  --color-accent-primary: #009B77;
  --color-accent-secondary: #FFBF00;
  --color-accent-cta: #F67891;
}

/* Dark mode colors - applied when the 'dark' class is present on the html element */
.dark {
  --color-background: #0F172A;
  --color-primary: #E2E8F0;
  --color-accent-primary: #10B981;
  --color-accent-secondary: #FFD700;
  --color-accent-cta: #F87171;
}

[x-cloak] {
    display: none;
}

/* Adds pointer cursor to buttons */
@layer base {
  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }
}

/* Defaults hover styles on all devices */
@custom-variant hover (&:hover);
