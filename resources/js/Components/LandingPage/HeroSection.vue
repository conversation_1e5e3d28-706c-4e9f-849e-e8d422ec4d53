<template>
	<section class="py-16 md:py-24 px-4">
		<ParticlesBg
			class="absolute inset-0"
			:quantity="200"
			ease="100"
			color="#003B5C"
			staticity="10"
			refresh
		/>
		<div class="container mx-auto max-w-7xl relative z-10">
			<div class="flex flex-col md:flex-row items-center">
				<div class="w-full md:w-1/2 mb-10 md:mb-0 md:pr-8">
					<h1 class="text-4xl md:text-5xl lg:text-6xl font-bold font-galindo text-primary leading-tight mb-6">
						Booking Management,
						<span class="text-accent-primary">Simple</span> and <span
							class="text-accent-primary">Free</span>
					</h1>
					<p class="text-lg md:text-xl text-gray-700 mb-8">
						Booking Bear helps small accommodation owners accept bookings, manage availability, and stay in control—with setup so easy, you’ll be live before your kettle boils.
					</p>
					<div class="flex flex-col sm:flex-row gap-4 mb-8">
						<Link :href="$page.props.auth.user ? route('dashboard') : route('register')"
							class="bg-accent-cta hover:bg-[#e56a82] text-white px-6 py-3 rounded-md font-medium text-center text-lg">
							Get Started For Free
						</Link>
						<a href="#pricing"
							class="bg-white border-2 border-primary text-primary px-6 py-3 rounded-md font-medium text-center text-lg hover:bg-primary hover:text-white transition-colors">
							View Pricing
						</a>
					</div>
					<div class="flex flex-col sm:flex-row items-center gap-4 text-primary">
						<div class="flex items-center">
							<CheckCircleIcon class="w-5 h-5 text-accent-primary mr-2" />
							<span>No credit card required</span>
						</div>
						<div class="flex items-center">
							<CheckCircleIcon class="w-5 h-5 text-accent-primary mr-2" />
							<span>Start taking bookings in minutes!</span>
						</div>
					</div>
				</div>
				<div class="w-full md:w-1/2">
					<div class="bg-white p-2 rounded-2xl shadow-xl">
						<img src="../../../images/dashboard-preview.jpg" alt="Booking Bear dashboard preview" class="w-full h-auto rounded-xl shadow-sm" width="624" height="416" />
					</div>
				</div>
			</div>
		</div>
	</section>
</template>

<script setup lang="ts">
import { CheckCircle as CheckCircleIcon } from 'lucide-vue-next'
import { Link } from '@inertiajs/vue3';
import ParticlesBg from '@/Components/ParticlesBg.vue';
import { computed } from 'vue'

const isDark = computed(() => {
  return document.documentElement.classList.contains('dark');
});
</script>
