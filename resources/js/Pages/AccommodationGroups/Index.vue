<script setup>
import { ref, computed } from 'vue';
import { Head, Link, router, usePage } from '@inertiajs/vue3';
import axios from 'axios';
import DashboardLayout from '@/Layouts/DashboardLayout.vue';
import Card from '@/Components/Card.vue';
import Badge from '@/Components/Badge.vue';
import Alert from '@/Components/Alert.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import Modal from '@/Components/Modal.vue';
import ToggleSwitch from '@/Components/ToggleSwitch.vue';

const page = usePage();
const flashSuccess = computed(() => page.props.flash?.success);

// Define props
const props = defineProps({
    groups: {
        type: Array,
        required: true
    }
});

// Create local reactive copy of groups to avoid mutating props
const localGroups = ref([...props.groups]);

// Check if the accommodation group limit is reached
const isGroupLimitReached = computed(() => {
    if (!page.props.subscription || !page.props.subscription.features) return false;

    const groupFeature = page.props.subscription.features.find(feature => feature.name === 'Accommodation Groups');
    if (!groupFeature) return false;

    return groupFeature.remaining <= 0;
});

// Get the accommodation group feature for display
const groupFeature = computed(() => {
    if (!page.props.subscription || !page.props.subscription.features) return null;
    return page.props.subscription.features.find(feature => feature.name === 'Accommodation Groups');
});

// Count of published groups
const publishedGroupsCount = computed(() => {
    return localGroups.value.filter(group => group.published).length;
});

// Check if publishing is allowed based on subscription limits
const canPublishGroup = computed(() => {
    if (!groupFeature.value) return false;
    // Only disable publishing if we're trying to publish a new group
    return groupFeature.value.value > publishedGroupsCount.value;
});

// Check if we can toggle publish status
const canToggleGroupPublish = (group) => {
    // Always allow unpublishing
    if (group.published) return true;
    // For publishing, check the subscription limit
    return canPublishGroup.value;
};

// Percentage of published groups relative to the limit
const publishedPercentage = computed(() => {
    if (!groupFeature.value || groupFeature.value.value === 0) return 0;
    return Math.round((publishedGroupsCount.value / groupFeature.value.value) * 100);
});

// Delete confirmation
const showDeleteModal = ref(false);
const groupToDelete = ref(null);

// Alert state
const showAlert = ref(false);
const alertMessage = ref('');
const alertType = ref('success');
const isTogglingPublish = ref(false);

// Show alert helper
const showAlertMessage = (message, type = 'success') => {
    alertMessage.value = message;
    alertType.value = type;
    showAlert.value = true;
    
    // Hide alert after 3 seconds
    setTimeout(() => {
        showAlert.value = false;
    }, 3000);
};

const togglePublishStatus = async (group, newValue) => {
    if (isTogglingPublish.value) return;
    
    // Check if we're trying to publish and if we've reached the limit
    if (newValue && groupFeature.value) {
        const remaining = groupFeature.value.value - publishedGroupsCount.value;
        if (remaining <= 0) {
            showAlertMessage('You have reached the maximum number of published accommodation groups for your subscription.', 'danger');
            // Revert the toggle state
            const groupIndex = localGroups.value.findIndex(g => g.id === group.id);
            if (groupIndex !== -1) {
                localGroups.value[groupIndex].published = false;
            }
            return;
        }
    }
    
    isTogglingPublish.value = true;
    
    try {
        const routeName = newValue ? 'accommodation-groups.publish' : 'accommodation-groups.unpublish';
        const response = await axios.post(route(routeName, { accommodationGroup: group.id }));
        
        // Update the local state
        const groupIndex = localGroups.value.findIndex(g => g.id === group.id);
        if (groupIndex !== -1) {
            localGroups.value[groupIndex].published = response.data.published;
        }
        
        // Show success message
        showAlertMessage(response.data.message, 'success');
        
        // Reload subscription data to keep counts accurate
        router.reload({ only: ['subscription'] });
    } catch (error) {
        console.error('Error updating publish status:', error);
        showAlertMessage(error.response?.data?.message || 'Failed to update publish status. Please try again.', 'danger');
        // Revert the toggle state if there was an error
        const groupIndex = localGroups.value.findIndex(g => g.id === group.id);
        if (groupIndex !== -1) {
            localGroups.value[groupIndex].published = !newValue;
        }
    } finally {
        isTogglingPublish.value = false;
    }
};

const confirmDelete = (group) => {
    groupToDelete.value = group;
    showDeleteModal.value = true;
};

const deleteGroup = () => {
    router.delete(route('accommodation-groups.destroy', groupToDelete.value.id), {
        onSuccess: () => {
            showDeleteModal.value = false;
            groupToDelete.value = null;
        },
    });
};

const cancelDelete = () => {
    showDeleteModal.value = false;
    groupToDelete.value = null;
};

</script>

<template>
    <DashboardLayout title="Accommodation Groups">
        <Head title="Accommodation Groups" />

        <template #header>
            <!-- Alert for success/error messages -->
            <Alert 
                v-if="showAlert" 
                :type="alertType" 
                class="mb-4" 
                dismissible
                @dismiss="showAlert = false"
            >
                {{ alertMessage }}
            </Alert>
            
            <div class="flex flex-col space-y-4 md:flex-row md:justify-between md:items-center md:space-y-0">
                <div>
                    <h2 class="font-semibold text-xl text-primary dark:text-blue-400 leading-tight">
                        Accommodation Groups
                    </h2>
                    <div v-if="groupFeature" class="text-sm text-gray-600 dark:text-gray-300 mt-2">
                        <div class="flex items-center gap-2">
                            <span>{{ publishedGroupsCount }} of {{ groupFeature.value }} published groups</span>
                            <div class="w-32 bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                                <div 
                                    class="h-2.5 rounded-full transition-all duration-300"
                                    :class="{
                                        'bg-green-500': publishedPercentage < 80,
                                        'bg-yellow-500': publishedPercentage >= 80 && publishedPercentage < 100,
                                        'bg-red-500': publishedPercentage >= 100
                                    }"
                                    :style="{ width: Math.min(100, publishedPercentage) + '%' }"
                                ></div>
                            </div>
                            <span 
                                v-if="publishedGroupsCount >= groupFeature.value" 
                                class="text-red-500 dark:text-red-400 font-medium"
                            >
                                Limit reached
                            </span>
                            <span 
                                v-else-if="groupFeature.value - publishedGroupsCount <= 2" 
                                class="text-yellow-500 dark:text-yellow-400 font-medium"
                            >
                                {{ groupFeature.value - publishedGroupsCount }} remaining
                            </span>
                            <span v-else class="text-green-500 dark:text-green-400 font-medium">
                                {{ groupFeature.value - publishedGroupsCount }} remaining
                            </span>
                        </div>
                    </div>
                </div>
                <div>
                    <Link :href="route('accommodations.index')" class="inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md font-semibold text-xs text-gray-700 dark:text-gray-300 uppercase tracking-widest shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-accent-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-25 transition ease-in-out duration-150">
                        Back to Accommodations
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="flex justify-between items-center mb-6">
                    <h1 class="text-2xl font-semibold text-primary dark:text-blue-400">Manage Accommodation Groups</h1>
                    <Link :href="route('accommodation-groups.create')">
                        <PrimaryButton :disabled="isGroupLimitReached" class="flex items-center gap-2">
                            <svg class="flex-shrink-0 w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M5 12h14"></path>
                                <path d="M12 5v14"></path>
                            </svg>
                            Add New Group
                        </PrimaryButton>
                    </Link>
                </div>

                <!-- Subscription limit warning -->
                <div v-if="isGroupLimitReached" class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 px-4 py-3 rounded relative mb-4" role="alert">
                    <strong class="font-bold">Subscription Limit Reached!</strong>
                    <span class="block sm:inline"> You have reached the maximum number of accommodation groups ({{ groupFeature?.value }}) allowed in your current plan. Please upgrade your plan to add more groups.</span>
                </div>

                <Alert v-if="flashSuccess" class="mb-6" type="success">
                    {{ flashSuccess }}
                </Alert>

                <div v-if="localGroups.length === 0" class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="text-center py-12 px-6">
                        <div class="mx-auto h-24 w-24 text-gray-400 dark:text-gray-500 mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" class="w-full h-full">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 21h19.5m-18-18v18m2.25-18v18m13.5-18v18m2.25-18v18M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.75m-.75 3h.75m-.75 3h.75m-3.75-16.5h.75m-.75 3h.75m-.75 3h.75m-3.75 3h.75m-.75 3h.75" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                            Get started with accommodation groups
                        </h3>
                        <p class="text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto">
                            You'll need at least one accommodation group before you can start adding accommodations.
                            Accommodation groups help you organize and manage your properties efficiently.
                        </p>
                        <Link :href="route('accommodation-groups.create')">
                            <PrimaryButton :disabled="isGroupLimitReached" class="inline-flex items-center gap-2">
                                <svg class="flex-shrink-0 w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M5 12h14"></path>
                                    <path d="M12 5v14"></path>
                                </svg>
                                Create Your First Accommodation Group
                            </PrimaryButton>
                        </Link>
                        <div v-if="isGroupLimitReached" class="mt-4">
                            <p class="text-sm text-red-600 dark:text-red-400">
                                You have reached your accommodation group limit.
                                <Link :href="route('plans.index')" class="underline hover:no-underline">
                                    Upgrade your plan
                                </Link>
                                to create more groups.
                            </p>
                        </div>
                    </div>
                </div>

                <div v-else class="flex flex-col">
                    <div class="-m-1.5 overflow-x-auto">
                        <div class="p-1.5 min-w-full inline-block align-middle">
                            <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl shadow-sm overflow-hidden">
                                <!-- Table -->
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                    <thead class="bg-gray-50 dark:bg-gray-800">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-start text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Image</th>
                                            <th scope="col" class="px-6 py-3 text-start text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Name</th>
                                            <th scope="col" class="px-6 py-3 text-start text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Status</th>
                                            <th scope="col" class="px-6 py-3 text-start text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Description</th>
                                            <th scope="col" class="px-6 py-3 text-start text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Accommodations</th>
                                            <th scope="col" class="px-6 py-3 text-end text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                                        <tr v-for="group in localGroups" :key="group.id" class="hover:bg-gray-50 dark:hover:bg-gray-800">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="h-12 w-16 bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden">
                                                    <img v-if="group.image" :src="group.image.thumbnail" :alt="group.name" class="w-full h-full object-cover">
                                                    <div v-else class="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                        </svg>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-primary dark:text-blue-400">
                                                <Link :href="route('accommodation-groups.show', group.id)" class="hover:underline">
                                                    {{ group.name }}
                                                </Link>
                                                <div v-if="group.default_price && group.default_price !== '0.00'" class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                    Default Price: R{{ group.default_price }}
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <ToggleSwitch
                                                    v-model="group.published"
                                                    :id="'publish-toggle-' + group.id"
                                                    @change="togglePublishStatus(group, $event)"
                                                    on-label="Published"
                                                    off-label="Draft"
                                                    :disabled="!canToggleGroupPublish(group)"
                                                    class="ml-4"
                                                />
                                            </td>
                                            <td class="px-6 py-4 text-sm text-gray-600 dark:text-gray-400 max-w-xs truncate">
                                                {{ group.description || 'No description provided.' }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                                                <Link :href="route('accommodations.index', { group: group.id })" class="inline-flex items-center gap-x-1 text-sm text-accent-primary hover:text-accent-primary/80 dark:text-green-400 dark:hover:text-green-300">
                                                    <span>View Accommodations</span>
                                                    <svg class="flex-shrink-0 w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="m9 18 6-6-6-6"/>
                                                    </svg>
                                                </Link>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-end text-sm font-medium">
                                                <div class="flex justify-end space-x-2">
                                                    <Link :href="route('accommodation-groups.show', group.id)" class="inline-flex items-center gap-x-1 text-sm text-primary hover:text-primary-dark dark:text-blue-400 dark:hover:text-blue-300">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                                                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                                                        </svg>
                                                        <span>View</span>
                                                    </Link>
                                                    <Link :href="route('accommodation-groups.edit', group.id)" class="inline-flex items-center gap-x-1 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                                                        </svg>
                                                        <span>Edit</span>
                                                    </Link>
                                                    <button @click="confirmDelete(group)" class="inline-flex items-center gap-x-1 text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                                            <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                        </svg>
                                                        <span>Delete</span>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <Modal :show="showDeleteModal" @close="cancelDelete">
            <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Delete Accommodation Group
                </h2>

                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Are you sure you want to delete this accommodation group? Any accommodations in this group will be unassigned, not deleted.
                </p>

                <div class="mt-6 flex justify-end">
                    <button
                        type="button"
                        class="inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-500 rounded-md font-semibold text-xs text-gray-700 dark:text-gray-300 uppercase tracking-widest shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-25 transition ease-in-out duration-150 mr-3"
                        @click="cancelDelete"
                    >
                        Cancel
                    </button>

                    <button
                        type="button"
                        class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-500 active:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150"
                        @click="deleteGroup"
                    >
                        Delete Group
                    </button>
                </div>
            </div>
        </Modal>
    </DashboardLayout>
</template>
