<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->web(append: [
            \App\Http\Middleware\HandleInertiaRequests::class,
            \Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets::class,
            \App\Http\Middleware\RedirectToPlanSelection::class,
        ]);

        // Configure API middleware group
        $middleware->api(prepend: [
            // Add any API-specific middleware here
            \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
        ]);

		$middleware->validateCsrfTokens(except: [
			'paddle/*',
            'api/*',
			'/email/verification-notification',
		]);

        // Register custom middlewares
        $middleware->alias([
            'check.subscription' => \App\Http\Middleware\CheckSubscriptionFeatures::class,
            'ensure.plan' => \App\Http\Middleware\EnsureUserHasPlan::class,
            'ensure.accommodation.groups' => \App\Http\Middleware\EnsureUserHasAccommodationGroups::class,
            'widget.auth' => \App\Http\Middleware\WidgetAuthMiddleware::class,
            'abilities' => \Laravel\Sanctum\Http\Middleware\CheckAbilities::class,
            'ability' => \Laravel\Sanctum\Http\Middleware\CheckForAnyAbility::class,
        ]);

		$middleware->trustProxies(
			at: '*',
		);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
