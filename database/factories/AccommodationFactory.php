<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\User;
use App\Models\AccommodationGroup;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Accommodation>
 */
class AccommodationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'accommodation_group_id' => AccommodationGroup::factory(), // Always create a group since it's required
            'name' => $this->faker->company,
            'description' => $this->faker->text(),
            'address' => $this->faker->address(),
            'city' => $this->faker->city(),
            'province' => $this->faker->state(),
            'post_code' => $this->faker->postcode(),
            'country' => $this->faker->country(),
            'min_occupancy' => $this->faker->numberBetween(1, 2),
            'max_occupancy' => $this->faker->numberBetween(3, 6),
            'minimum_booking_notice' => $this->faker->numberBetween(0, 7),
        ];
    }

    /**
     * Indicate that the accommodation belongs to a group.
     *
     * @param AccommodationGroup|null $group
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function withGroup(?AccommodationGroup $group = null)
    {
        return $this->state(function (array $attributes) use ($group) {
            // If no group is provided, create a new one
            if (!$group) {
                $group = AccommodationGroup::factory()->create([
                    'user_id' => $attributes['user_id'],
                ]);
            }

            return [
                'accommodation_group_id' => $group->id,
            ];
        });
    }

    /**
     * Configure the model factory.
     *
     * @return $this
     */
    // public function configure()
    // {
    //     return $this->afterCreating(function ($accommodation) {
    //         AccommodationPrice::factory()->create([
    //             'accommodation_id' => $accommodation->id,
    //             'type' => 'default',
    //             'priority' => 0
    //         ]);
    //     });
    // }
}
