<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Accommodation;
use App\Models\AccommodationGroup;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, handle any existing accommodations without accommodation groups
        $this->handleExistingAccommodations();

        // Then make the column non-nullable and add foreign key constraint
        Schema::table('accommodations', function (Blueprint $table) {
            // Make accommodation_group_id non-nullable
            $table->unsignedBigInteger('accommodation_group_id')->nullable(false)->change();

            // Add the foreign key constraint with CASCADE
            $table->foreign('accommodation_group_id')
                  ->references('id')
                  ->on('accommodation_groups')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('accommodations', function (Blueprint $table) {
            // Drop the foreign key constraint
            $table->dropForeign(['accommodation_group_id']);

            // Make accommodation_group_id nullable again
            $table->unsignedBigInteger('accommodation_group_id')->nullable()->change();
        });
    }

    /**
     * Handle existing accommodations that don't have accommodation groups
     */
    private function handleExistingAccommodations(): void
    {
        // Get all accommodations without accommodation groups
        $accommodationsWithoutGroups = Accommodation::whereNull('accommodation_group_id')->get();

        if ($accommodationsWithoutGroups->isEmpty()) {
            return;
        }

        // Group accommodations by user_id and team_id
        $groupedAccommodations = $accommodationsWithoutGroups->groupBy(function ($accommodation) {
            return $accommodation->user_id . '_' . ($accommodation->team_id ?? 'null');
        });

        foreach ($groupedAccommodations as $accommodations) {
            $firstAccommodation = $accommodations->first();

            // Create a default accommodation group for this user/team
            $defaultGroup = AccommodationGroup::create([
                'name' => 'Default Group',
                'description' => 'Automatically created group for existing accommodations',
                'user_id' => $firstAccommodation->user_id,
                'team_id' => $firstAccommodation->team_id,
                'published' => true,
            ]);

            // Update all accommodations in this group to use the new default group
            foreach ($accommodations as $accommodation) {
                $accommodation->update(['accommodation_group_id' => $defaultGroup->id]);
            }
        }
    }
};
